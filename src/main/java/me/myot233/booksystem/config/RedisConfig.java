package me.myot233.booksystem.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer; // Or another serializer

@Configuration
public class RedisConfig {



    // If using Jedis instead of Lettuce:
    /*
    import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
    import redis.clients.jedis.JedisPoolConfig;

    @Bean
    public JedisConnectionFactory jedisConnectionFactory() {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration("localhost", 6379);
        // config.setPassword("yourpassword");
        // config.setDatabase(0);

        // Example for Jedis Pool configuration
        // JedisPoolConfig poolConfig = new JedisPoolConfig();
        // poolConfig.setMaxTotal(10);
        // poolConfig.setMaxIdle(5);
        // poolConfig.setMinIdle(1);
        // poolConfig.setTestOnBorrow(true);
        // poolConfig.setTestOnReturn(true);
        // poolConfig.setTestWhileIdle(true);
        // return new JedisConnectionFactory(config, JedisClientConfiguration.builder().usePooling().poolConfig(poolConfig).build());

        return new JedisConnectionFactory(config);
    }
    */

    @Bean
    public RedisTemplate<String, Object> redisTemplate(@Autowired RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory); // Or jedisConnectionFactory()
        // Key serializer
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        // Value serializer@
        // Using Jackson2JsonRedisSerializer for Object serialization
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        // Or use JdkSerializationRedisSerializer for default Java serialization

        template.afterPropertiesSet();
        return template;
    }

    // You might also want a StringRedisTemplate for string-only operations
    /*
    import org.springframework.data.redis.core.StringRedisTemplate;

    @Bean
    public StringRedisTemplate stringRedisTemplate() {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate();
        stringRedisTemplate.setConnectionFactory(redisConnectionFactory()); // Or jedisConnectionFactory()
        return stringRedisTemplate;
    }
    */
}